import { initializeApp, getApps, getApp, FirebaseApp } from 'firebase/app';
import { getAuth, Auth } from 'firebase/auth';

const firebaseConfig = {
  apiKey: "AIzaSyD1NiGkzuKgXm_7Goj7UrIwqtP6OcrFwn0",
  authDomain: "mizan-money-app.firebaseapp.com",
  projectId: "mizan-money-app",
  storageBucket: "mizan-money-app.firebasestorage.app",
  messagingSenderId: "295810463616",
  appId: "1:295810463616:web:7a719106cfe72889637365",
  measurementId: "G-P97WJP3EJY"
};

// Initialize Firebase app (only if not already initialized)
let app: FirebaseApp;
if (getApps().length === 0) {
  app = initializeApp(firebaseConfig);
  console.log('✅ Firebase app initialized');
} else {
  app = getApp();
  console.log('✅ Firebase app already exists, using existing instance');
}

// Initialize Auth - use getAuth for Expo compatibility
const auth: Auth = getAuth(app);
console.log('✅ Firebase Auth initialized');

export { app, auth };
