import { initializeApp, getApps, getApp } from 'firebase/app';
import { getAuth, initializeAuth, getReactNativePersistence } from 'firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';

const firebaseConfig = {
  apiKey: "AIzaSyD1NiGkzuKgXm_7Goj7UrIwqtP6OcrFwn0",
  authDomain: "mizan-money-app.firebaseapp.com",
  projectId: "mizan-money-app",
  storageBucket: "mizan-money-app.firebasestorage.app",
  messagingSenderId: "295810463616",
  appId: "1:295810463616:web:7a719106cfe72889637365",
  measurementId: "G-P97WJP3EJY"
};

// Initialize Firebase app (only if not already initialized)
let app;
if (getApps().length === 0) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApp();
}

// Initialize Auth with persistence for React Native
let auth;
try {
  auth = initializeAuth(app, {
    persistence: getReactNativePersistence(AsyncStorage)
  });
} catch (error) {
  // If auth is already initialized, get the existing instance
  auth = getAuth(app);
}

export { app, auth };
