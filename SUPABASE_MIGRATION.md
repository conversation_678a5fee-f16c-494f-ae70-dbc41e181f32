# Supabase Authentication Migration

This document outlines the migration from Firebase to Supabase authentication for the Mizan Money App.

## Overview

The app has been migrated from Firebase Authentication to Supabase Authentication with the following key features:

- Email/password authentication
- Phone number verification with SMS OTP (Kenya-compatible)
- Biometric authentication (Face ID/Touch ID/Fingerprint)
- User profile management
- Secure token storage

## Setup Instructions

### 1. Supabase Project Setup

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and anon key
3. Copy `.env.example` to `.env` and fill in your Supabase credentials:

```bash
cp .env.example .env
```

### 2. Database Schema

Run the following SQL in your Supabase SQL editor to create the required tables:

```sql
-- Enable RLS
ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

-- Create users table
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  phone_number TEXT,
  full_name TEXT,
  gender TEXT,
  date_of_birth DATE,
  interests TEXT[],
  employment_status TEXT,
  monthly_income DECIMAL,
  monthly_expenditure DECIMAL,
  financial_exposure INTEGER,
  kyc_status TEXT DEFAULT 'pending' CHECK (kyc_status IN ('pending', 'in_progress', 'completed', 'rejected')),
  kyc_data JSONB,
  is_active BOOLEAN DEFAULT true,
  email_verified BOOLEAN DEFAULT false,
  phone_verified BOOLEAN DEFAULT false,
  biometric_enabled BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create OTP verifications table
CREATE TABLE public.otp_verifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  phone_number TEXT NOT NULL,
  otp_code TEXT NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  verified BOOLEAN DEFAULT false,
  attempts INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Round-Up settings table
CREATE TABLE public.roundup_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE UNIQUE,
  is_enabled BOOLEAN DEFAULT false,
  round_up_method TEXT DEFAULT 'nearest_dollar' CHECK (round_up_method IN ('nearest_dollar', 'custom_amount')),
  custom_amount DECIMAL,
  default_destination TEXT DEFAULT 'investment' CHECK (default_destination IN ('investment', 'charity')),
  minimum_roundup DECIMAL DEFAULT 0.01,
  maximum_roundup DECIMAL DEFAULT 5.00,
  monthly_limit DECIMAL,
  excluded_categories TEXT[],
  investment_allocation JSONB,
  charity_allocation JSONB,
  auto_invest_threshold DECIMAL DEFAULT 10.00,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Row Level Security Policies
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own OTP verifications" ON public.otp_verifications
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own roundup settings" ON public.roundup_settings
  FOR ALL USING (auth.uid() = user_id);

-- Function to handle user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, email_verified)
  VALUES (NEW.id, NEW.email, NEW.email_confirmed_at IS NOT NULL);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_users_updated_at
  BEFORE UPDATE ON public.users
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_roundup_settings_updated_at
  BEFORE UPDATE ON public.roundup_settings
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
```

### 3. SMS Service Setup

#### Option A: Africa's Talking (Recommended for Kenya)

1. Sign up at [africastalking.com](https://africastalking.com)
2. Get your API key and username
3. Add them to your `.env` file

#### Option B: Twilio (Alternative)

1. Sign up at [twilio.com](https://twilio.com)
2. Get your Account SID, Auth Token, and phone number
3. Add them to your `.env` file

### 4. Environment Variables

Create a `.env` file with the following variables:

```env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_AFRICAS_TALKING_API_KEY=your_africas_talking_api_key
EXPO_PUBLIC_AFRICAS_TALKING_USERNAME=your_africas_talking_username
```

## Key Changes Made

### 1. Authentication Hook (`hooks/useAuth.ts`)

- Replaced `useFirebase` with `useAuth`
- Handles Supabase authentication state
- Manages user profile data
- Provides sign up, sign in, sign out functions

### 2. SMS Service (`services/smsService.ts`)

- Kenya-compatible phone number formatting
- OTP generation and verification
- Rate limiting for OTP requests
- Integration with Africa's Talking API

### 3. Biometric Service (`services/biometricService.ts`)

- Cross-platform biometric authentication
- Secure storage of biometric preferences
- Support for Face ID, Touch ID, and Fingerprint

### 4. Updated Screens

- **AuthScreen**: Uses Supabase authentication
- **PhoneNumberScreen**: Sends OTP via SMS service
- **OTPScreen**: Verifies OTP and updates user profile
- **ProfileScreen**: Shows user data and biometric toggle

### 5. Database Types (`types/supabase.ts`)

- TypeScript types for all database tables
- Type-safe database operations
- Auto-generated from Supabase schema

## Features

### Authentication Flow

1. **Sign Up**: Email/password → Phone verification → KYC flow
2. **Sign In**: Email/password or biometric authentication
3. **Phone Verification**: SMS OTP with Kenya phone number support
4. **Biometric**: Optional Face ID/Touch ID/Fingerprint login

### Security Features

- Row Level Security (RLS) enabled
- Secure token storage with Expo SecureStore
- Biometric authentication with device security
- OTP rate limiting and expiration

### User Management

- Complete user profiles with KYC data
- Phone number verification status
- Biometric login preferences
- Round-up settings for financial features

## Testing

1. **Authentication**: Test sign up, sign in, and sign out flows
2. **Phone Verification**: Test with valid Kenyan phone numbers
3. **Biometric**: Test on physical devices with biometric hardware
4. **Profile Management**: Test profile updates and data persistence

## Migration Notes

- All Firebase-specific code has been removed
- User data structure has been enhanced for KYC compliance
- Phone number verification is now mandatory for full account access
- Biometric authentication is optional but recommended

## Troubleshooting

### Common Issues

1. **Supabase Connection**: Check URL and anon key in `.env`
2. **SMS Not Sending**: Verify Africa's Talking credentials
3. **Biometric Not Working**: Test on physical device with enrolled biometrics
4. **Database Errors**: Check RLS policies and table permissions

### Debug Mode

Enable debug logging by setting:
```typescript
console.log('Debug mode enabled');
```

## Next Steps

1. Set up production Supabase project
2. Configure SMS service for production
3. Test on physical devices
4. Set up monitoring and analytics
5. Configure backup and recovery procedures
