{"name": "my-expo-app", "version": "1.0.0", "scripts": {"android": "expo run:android", "ios": "expo run:ios", "start": "expo start", "prebuild": "node ./fix-wheel-picker.js && expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web", "postinstall": "npx patch-package", "fix-wheel-picker": "node ./fix-wheel-picker.js"}, "dependencies": {"@expo-google-fonts/inter": "^0.3.0", "@expo-google-fonts/poppins": "^0.3.1", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "latest", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-community/slider": "^4.5.6", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "7.3.12", "@react-navigation/native": "^7.1.8", "@react-navigation/native-stack": "7.3.12", "@react-navigation/stack": "^7.3.2", "@shopify/react-native-skia": "v2.0.0-next.4", "expo": "^53.0.9", "expo-camera": "~16.1.6", "expo-dev-client": "~5.1.8", "expo-font": "~13.3.1", "expo-linear-gradient": "~14.1.4", "expo-local-authentication": "^16.0.4", "expo-media-library": "~17.1.6", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.8", "expo-sqlite": "^15.2.10", "expo-status-bar": "~2.2.3", "firebase": "^11.9.0", "lucide-react-native": "0.509.0", "nativewind": "latest", "patch-package": "^8.0.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-confetti-cannon": "^1.5.2", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-wheel-picker": "^1.2.0", "react-native-wheel-scrollview-picker": "^2.0.9", "sonner-native": "0.19.1", "victory-native": "^41.17.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "eslint": "^9.25.1", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.5", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "main": "node_modules/expo/AppEntry.js", "private": true}