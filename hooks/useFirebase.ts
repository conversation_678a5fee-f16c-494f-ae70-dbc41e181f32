import { useEffect, useState } from 'react';
import { auth } from '../firebaseConfig';

export const useFirebase = () => {
  const [isFirebaseReady, setIsFirebaseReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeFirebase = async () => {
      try {
        // Wait a bit to ensure Firebase is fully initialized
        await new Promise(resolve => setTimeout(resolve, 100));
        
        if (auth) {
          setIsFirebaseReady(true);
          console.log('Firebase Auth is ready');
        } else {
          throw new Error('Firebase Auth not initialized');
        }
      } catch (err) {
        console.error('Firebase initialization error:', err);
        setError(err instanceof Error ? err.message : 'Firebase initialization failed');
      }
    };

    initializeFirebase();
  }, []);

  return { isFirebaseReady, error, auth: isFirebaseReady ? auth : null };
};
