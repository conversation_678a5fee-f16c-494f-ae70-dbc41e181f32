import { useEffect, useState } from 'react';
import { Auth } from 'firebase/auth';
import { auth } from '../firebaseConfig';
import { testFirebaseInitialization, logFirebaseStatus } from '../utils/firebaseTest';

interface UseFirebaseReturn {
  isFirebaseReady: boolean;
  error: string | null;
  auth: Auth | null;
}

export const useFirebase = (): UseFirebaseReturn => {
  const [isFirebaseReady, setIsFirebaseReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeFirebase = async () => {
      try {
        // Wait a bit to ensure Firebase is fully initialized
        await new Promise<void>(resolve => setTimeout(resolve, 100));

        // Test Firebase initialization
        const isInitialized = testFirebaseInitialization();
        logFirebaseStatus();

        if (auth && isInitialized) {
          setIsFirebaseReady(true);
          console.log('✅ Firebase Auth is ready');
        } else {
          throw new Error('Firebase Auth not initialized');
        }
      } catch (err) {
        console.error('❌ Firebase initialization error:', err);
        setError(err instanceof Error ? err.message : 'Firebase initialization failed');
      }
    };

    initializeFirebase();
  }, []);

  return {
    isFirebaseReady,
    error,
    auth: isFirebaseReady ? auth : null
  };
};
