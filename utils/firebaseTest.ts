import { auth } from '../firebaseConfig';

export const testFirebaseInitialization = (): boolean => {
  try {
    console.log('Testing Firebase initialization...');
    
    if (!auth) {
      console.error('❌ Firebase auth is null or undefined');
      return false;
    }
    
    console.log('✅ Firebase auth object exists');
    console.log('Auth app:', auth.app?.name || 'No app name');
    console.log('Auth config:', auth.config || 'No config');
    
    return true;
  } catch (error) {
    console.error('❌ Firebase test failed:', error);
    return false;
  }
};

export const logFirebaseStatus = (): void => {
  console.log('=== Firebase Status ===');
  console.log('Auth instance:', !!auth);
  console.log('Current user:', auth?.currentUser?.uid || 'No user');
  console.log('App name:', auth?.app?.name || 'No app');
  console.log('=====================');
};
